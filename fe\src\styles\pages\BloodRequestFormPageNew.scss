.blood-request-form-page {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 20px 0;

  .page-header {
    text-align: center;
    margin-bottom: 1rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 1rem;

    .header-content {
      display: inline-flex;
      align-items: center;
      gap: 1rem;
      padding: 3rem 1.5rem;
      background: #f8fafc;
      border: 1px solid #e2e8f0;
      border-radius: 12px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
      width: 100%;
      max-width: 750px;
      justify-content: center;
      min-height: 120px;
    }

    .header-icon {
      width: 48px;
      height: 48px;
      background: #3b82f6;
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 20px;
      flex-shrink: 0;
    }

    .header-text {
      .header-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0;
        color: #1e293b;
        line-height: 1.2;
      }

      .header-subtitle {
        font-size: 1rem;
        margin: 0;
        color: #64748b;
        font-weight: 400;
        line-height: 1.3;
      }
    }

    @media (max-width: 768px) {
      padding: 0 0.5rem;

      .header-content {
        padding: 2rem 1rem;
        gap: 0.75rem;
        max-width: 100%;
        min-height: 100px;
      }

      .header-icon {
        width: 40px;
        height: 40px;
        font-size: 16px;
        border-radius: 8px;
      }

      .header-text {
        .header-title {
          font-size: 1.25rem;
        }

        .header-subtitle {
          font-size: 0.875rem;
        }
      }
    }
  }

  .progress-container {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e2e8f0;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;

    .step-indicator {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;
        position: relative;

        &:not(:last-child)::after {
          content: "";
          position: absolute;
          top: 20px;
          left: 60%;
          width: 80%;
          height: 2px;
          background: #dee2e6;
          z-index: 1;
        }

        &.active::after {
          background: #3b82f6;
        }

        &.completed::after {
          background: #10b981;
        }

        .step-icon {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f8fafc;
          color: #64748b;
          font-size: 16px;
          margin-bottom: 8px;
          border: 2px solid #e2e8f0;
          transition: all 0.3s ease;
          z-index: 2;
          position: relative;

          &.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
          }

          &.completed {
            background: #10b981;
            color: white;
            border-color: #10b981;
          }
        }

        .step-title {
          font-size: 12px;
          font-weight: 600;
          text-align: center;
          color: #64748b;
          max-width: 70px;

          &.active {
            color: #3b82f6;
          }

          &.completed {
            color: #10b981;
          }
        }
      }
    }

    .progress-bar {
      height: 6px;
      border-radius: 3px;
      background: #e2e8f0;

      .progress-bar {
        background: #3b82f6;
        border-radius: 3px;
        transition: width 0.3s ease;
      }
    }
  }

  .form-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    overflow: hidden;
    max-width: 800px;
    margin: 0 auto;

    .card-header {
      background: #3b82f6;
      color: white;
      border: none;
      padding: 20px 24px;

      h5 {
        margin: 0;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 1.1rem;
      }

      small {
        opacity: 0.9;
        font-weight: 400;
        margin-top: 4px;
        display: block;
      }
    }

    .card-body {
      padding: 32px 24px;
    }
  }

  // Step content styling
  .step-content {
    padding: 1rem 0;
  }

  // Form field alignment and spacing
  .form-group {
    margin-bottom: 1.75rem;

    &:last-child {
      margin-bottom: 0;
    }

    // Medical condition group takes full width
    &.medical-condition-group {
      width: 100%;

      .form-control {
        width: 100%;
        max-width: 100%;
      }
    }
  }

  // Form label improvements
  .form-label {
    margin-bottom: 0.5rem;
    display: block;
    line-height: 1.4;
  }

  // Form control improvements
  .form-control,
  .form-select {
    line-height: 1.5;

    &:focus {
      transform: translateY(-1px);
      box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.15),
        0 2px 4px rgba(0, 0, 0, 0.1);
    }

    // Unit display styling
    &.unit-display {
      background-color: #f8f9fa;
      color: #6c757d;
      font-weight: 600;
      text-align: center;
      cursor: not-allowed;
    }

    // Medical condition textarea
    &.medical-condition-textarea {
      min-height: 120px;
      resize: both;
      font-family: inherit;
      line-height: 1.6;
      width: 100%;

      &::placeholder {
        color: #6c757d;
        font-style: italic;
      }

      &:focus {
        min-height: 120px;
      }
    }
  }

  .form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-bottom: 2rem;
    align-items: flex-start;

    &:last-child {
      margin-bottom: 0;
    }

    .form-col {
      flex: 1;
      min-width: 0;

      &.form-col-2 {
        flex: 0 0 auto;
        width: 140px;
        min-width: 120px;
      }

      &.form-col-3 {
        flex: 0 0 auto;
        width: 160px;
        min-width: 140px;
      }

      &.form-col-4 {
        flex: 0 0 auto;
        width: 200px;
        min-width: 180px;
      }

      &.form-col-6 {
        flex: 0 0 auto;
        width: calc(50% - 0.75rem);
        min-width: 250px;
      }
    }

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1.25rem;

      .form-col {
        width: 100% !important;
        flex: none;
        min-width: auto !important;
      }
    }
  }

  // Bootstrap row overrides for better spacing
  .row {
    &.g-3 {
      --bs-gutter-x: 1rem;
      --bs-gutter-y: 1rem;
    }

    &.mb-4 {
      margin-bottom: 1.5rem !important;
    }
  }

  .col-md-6,
  .col-md-4,
  .col-md-3,
  .col-md-2 {
    @media (max-width: 767px) {
      margin-bottom: 1rem;
    }
  }

  // Form styling
  .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    font-size: 14px;

    .text-danger {
      color: #dc3545 !important;
    }
  }

  .form-control,
  .form-select {
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 12px 16px;
    font-size: 14px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

    &:focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.15);
    }

    &.is-invalid {
      border-color: #ef4444;
    }

    &::placeholder {
      color: #6c757d;
    }
  }

  .invalid-feedback {
    display: block;
    font-size: 13px;
    color: #ef4444;
    margin-top: 4px;
  }

  // Radio buttons styling
  .radio-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-top: 12px;

    .form-check {
      margin: 0;
      padding: 16px;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      background: #f8fafc;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;

      &:hover {
        background: #f1f5f9;
        border-color: #cbd5e1;
      }

      &:has(.form-check-input:checked) {
        background: #eff6ff;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      .form-check-input {
        margin-top: 0;
        margin-right: 12px;
        width: 18px;
        height: 18px;

        &:checked {
          background-color: #3b82f6;
          border-color: #3b82f6;
        }

        &:focus {
          box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.15);
        }
      }

      .form-check-label {
        font-weight: 500;
        color: #1e293b;
        cursor: pointer;
        margin: 0;
        flex: 1;
        line-height: 1.4;
      }
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 12px;
    }
  }

  // Textarea styling
  textarea.form-control {
    resize: vertical;
    min-height: 100px;
  }

  // Navigation buttons
  .navigation-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e9ecef;

    .btn {
      padding: 12px 24px;
      font-weight: 600;
      border-radius: 8px;
      font-size: 14px;
      display: flex;
      align-items: center;
      gap: 8px;
      min-width: 120px;
      justify-content: center;

      &.btn-primary {
        background-color: #3b82f6;
        border-color: #3b82f6;

        &:hover:not(:disabled) {
          background-color: #2563eb;
          border-color: #1d4ed8;
        }
      }

      &.btn-success {
        background-color: #10b981;
        border-color: #10b981;

        &:hover:not(:disabled) {
          background-color: #059669;
          border-color: #047857;
        }
      }

      &.btn-outline-secondary {
        color: #6c757d;
        border-color: #6c757d;

        &:hover:not(:disabled) {
          background-color: #6c757d;
          color: white;
        }
      }
    }
  }

  // Success/Error result cards
  .success-card,
  .error-card {
    background: white;
    border-radius: 16px;
    border: none;
    overflow: hidden;
    position: relative;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;

    .card-body {
      text-align: center;
      padding: 60px 40px;
      position: relative;
    }

    .result-icon {
      font-size: 80px;
      margin-bottom: 25px;
      display: inline-block;
      padding: 20px;
      border-radius: 50%;
      width: 120px;
      height: 120px;
      line-height: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 25px auto;
    }

    .result-title {
      font-size: 32px;
      font-weight: 700;
      margin-bottom: 20px;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .result-description {
      font-size: 18px;
      color: #6b7280;
      margin-bottom: 40px;
      line-height: 1.6;
      max-width: 500px;
      margin-left: auto;
      margin-right: auto;
    }
  }

  .success-card {
    border: 1px solid #d1fae5;
    background: #f0fdf4;
    box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.1);

    .result-icon {
      color: #10b981;
      background: rgba(16, 185, 129, 0.1);
      animation: bounce 0.5s ease-in-out;
      font-size: 4rem;
      margin-bottom: 20px;
    }

    .result-title {
      color: #065f46;
      font-weight: 600;
    }
  }

  .error-card {
    border: 2px solid #ef4444;
    background: linear-gradient(
      135deg,
      rgba(239, 68, 68, 0.05) 0%,
      rgba(239, 68, 68, 0.1) 100%
    );

    .result-icon {
      color: #ef4444;
      background: rgba(239, 68, 68, 0.1);
      animation: shake 0.5s ease-in-out;
    }

    .result-title {
      color: #ef4444;
    }
  }

  // Request summary styling
  .request-summary {
    background: #f8fafc;
    border-radius: 15px;
    padding: 25px;
    margin: 25px 0;
    text-align: left;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

    .summary-title {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 20px;
      color: #2d3748;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .summary-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid rgba(226, 232, 240, 0.6);

      &:last-child {
        border-bottom: none;
      }

      .label {
        font-weight: 600;
        color: #4a5568;
        font-size: 15px;
      }

      .value {
        color: #2d3748;
        font-weight: 500;

        &.blood-type {
          background: #fee2e2;
          color: #dc2626;
          padding: 6px 16px;
          border-radius: 25px;
          font-weight: 700;
          font-size: 14px;
          border: 1px solid #fecaca;
        }

        &.request-id {
          background: #e0e7ff;
          color: #4338ca;
          padding: 6px 16px;
          border-radius: 25px;
          font-weight: 700;
          font-size: 14px;
          border: 1px solid #c7d2fe;
        }
      }
    }
  }

  // Alert styling
  .alert {
    border-radius: 15px;
    border: none;
    padding: 20px;
    margin-bottom: 25px;

    &.alert-info {
      background: #f0fdfa;
      color: #234e52;
      border-left: 4px solid #38b2ac;
      border: 1px solid #a7f3d0;
    }

    .alert-heading {
      font-weight: 700;
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  // Home button styling
  .home-button {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
    border: none !important;
    border-radius: 10px !important;
    font-weight: 600 !important;
    font-size: 15px !important;
    letter-spacing: 0.3px !important;
    min-width: 180px;
    padding: 12px 24px !important;
    margin-right: 16px !important;
    box-shadow: 0 3px 12px rgba(16, 185, 129, 0.3) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
      );
      transition: left 0.5s;
    }
  }

  // Retry button styling
  .retry-button {
    background: transparent !important;
    border: 2px solid #3b82f6 !important;
    color: #3b82f6 !important;
    border-radius: 10px !important;
    font-weight: 600 !important;
    font-size: 15px !important;
    letter-spacing: 0.3px !important;
    min-width: 180px;
    padding: 12px 24px !important;
    box-shadow: 0 3px 12px rgba(59, 130, 246, 0.2) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(59, 130, 246, 0.1),
        transparent
      );
      transition: left 0.5s;
    }

    &:hover {
      transform: translateY(-2px) !important;
      box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4) !important;
      background: linear-gradient(135deg, #059669 0%, #047857 100%) !important;

      &::before {
        left: 100%;
      }
    }

    &:active {
      transform: translateY(0) !important;
      box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3) !important;
    }

    &:focus {
      box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3),
        0 0 0 3px rgba(16, 185, 129, 0.2) !important;
    }

    .fa-home {
      font-size: 18px;
      transition: transform 0.3s ease;
    }

    &:hover .fa-home {
      transform: scale(1.1);
    }
  }

  .retry-button {
    &:hover {
      background: #3b82f6 !important;
      color: white !important;
      border-color: #3b82f6 !important;
      transform: translateY(-2px) !important;
      box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4) !important;

      &::before {
        left: 100%;
      }
    }

    &:active {
      transform: translateY(0) !important;
      box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2) !important;
    }

    &:focus {
      box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2),
        0 0 0 3px rgba(59, 130, 246, 0.2) !important;
    }

    .fa-redo {
      font-size: 18px;
      transition: transform 0.3s ease;
    }

    &:hover .fa-redo {
      transform: rotate(360deg);
    }
  }

  // Responsive design
  @media (max-width: 992px) {
    .progress-container {
      padding: 20px;
      margin-bottom: 20px;
    }

    .form-card .card-body {
      padding: 30px 25px;
    }

    .page-header h1 {
      font-size: 2.5rem;
    }
  }

  @media (max-width: 768px) {
    .page-header {
      margin-bottom: 1.5rem;

      h1 {
        font-size: 2rem;
      }

      p {
        font-size: 1rem;
      }
    }

    .progress-container {
      padding: 15px;
      border-radius: 15px;

      .step-indicator .step {
        .step-icon {
          width: 40px;
          height: 40px;
          font-size: 16px;
        }

        .step-title {
          font-size: 11px;
          max-width: 60px;
        }
      }
    }

    .form-card {
      border-radius: 20px;

      .card-header {
        padding: 20px;

        h5 {
          font-size: 1.1rem;
        }
      }

      .card-body {
        padding: 25px 20px;
      }
    }

    .radio-group {
      grid-template-columns: 1fr;
      gap: 12px;

      .form-check {
        padding: 12px;
      }
    }

    .medical-condition-textarea {
      min-height: 120px !important;

      &:focus {
        min-height: 140px !important;
      }
    }

    .navigation-buttons {
      flex-direction: column;
      gap: 15px;
      padding-top: 25px;

      .btn {
        width: 100%;
        min-width: auto;
      }

      .home-button {
        min-width: 100%;
        font-size: 15px !important;
      }
    }

    .success-card,
    .error-card {
      .card-body {
        padding: 40px 25px;
      }

      .result-icon {
        font-size: 60px;
      }

      .result-title {
        font-size: 24px;
      }

      .result-description {
        font-size: 16px;
      }
    }

    .request-summary {
      padding: 20px;

      .summary-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        padding: 15px 0;

        .value {
          align-self: flex-end;
        }
      }
    }
  }

  @media (max-width: 576px) {
    .form-control,
    .form-select {
      padding: 12px 15px;
      font-size: 16px; // Prevent zoom on iOS
    }

    .progress-container .step-indicator .step {
      .step-title {
        font-size: 10px;
        max-width: 50px;
      }
    }
  }
}

// Animations
@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}
