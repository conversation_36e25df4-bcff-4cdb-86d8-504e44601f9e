import React, { useEffect, useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import GuestHomePage from "../guest/GuestHomePage";
import MemberNavbar from "../../components/member/MemberNavbar";
import UnifiedModal from "../../components/member/UnifiedModal";
import authService from "../../services/authService";
import userInfoService from "../../services/userInfoService";
import { getUserName } from "../../utils/userUtils";
import useBloodRequestCheck from "../../hooks/useBloodRequestCheck";
import blood1 from "../../assets/images/blood1.jpg";
import "../../styles/pages/MemberHomePage.scss";

const MemberHomePage = () => {
  const navigate = useNavigate();
  const user = authService.getCurrentUser();
  const userName = getUserName();

  // Blood request check hook
  const { isChecking, pendingRequest, checkPendingRequest } =
    useBloodRequestCheck();

  // Modal state
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState("profile-incomplete"); // 'profile-incomplete' or 'blood-request-status'
  const [modalContext, setModalContext] = useState("general"); // 'donation', 'request', or 'general'
  const [profileCheckResult, setProfileCheckResult] = useState(null);

  // Handle blood donation button click
  const handleBloodDonationClick = async (e) => {
    e.preventDefault();

    console.log("🔍 Checking profile completeness for blood donation...");

    try {
      // Check if profile is complete
      const userInfo = await userInfoService.getUserInfo(user.id);
      const profileCheck = userInfoService.checkProfileCompleteness(userInfo);

      if (!profileCheck.isComplete) {
        // Profile incomplete - show modal with missing fields
        setProfileCheckResult(profileCheck);
        setModalType("profile-incomplete");
        setModalContext("donation");
        setShowModal(true);
        return;
      }

      // Profile is complete - navigate to form
      navigate("/member/blood-donation-form");
    } catch (error) {
      console.error("Error checking profile completeness:", error);
      // If there's an error checking profile, still allow navigation
      navigate("/member/blood-donation-form");
    }
  };

  // Handle blood request button click
  const handleBloodRequestClick = async (e) => {
    e.preventDefault();

    console.log("🔍 Checking profile completeness and pending requests...");

    try {
      // First check if profile is complete
      const userInfo = await userInfoService.getUserInfo(user.id);
      const profileCheck = userInfoService.checkProfileCompleteness(userInfo);

      if (!profileCheck.isComplete) {
        // Profile incomplete - show modal with missing fields
        setProfileCheckResult(profileCheck);
        setModalType("profile-incomplete");
        setModalContext("request");
        setShowModal(true);
        return;
      }

      // Profile is complete, now check for pending requests
      const result = await checkPendingRequest();

      if (result.success) {
        if (result.hasPendingRequest) {
          // Show modal with pending request info
          setModalType("blood-request-status");
          setModalContext("request");
          setShowModal(true);
        } else {
          // No pending request - navigate to form
          navigate("/member/blood-request-form");
        }
      } else {
        // Error checking - still allow navigation but log error
        console.error("Error checking pending requests:", result.error);
        navigate("/member/blood-request-form");
      }
    } catch (error) {
      console.error("Error checking profile completeness:", error);
      // If there's an error checking profile, still allow navigation
      navigate("/member/blood-request-form");
    }
  };

  // Lấy thông tin nhóm máu từ hồ sơ cá nhân
  const getBloodTypeInfo = () => {
    // Thử lấy từ localStorage trước
    const storedInfo = JSON.parse(localStorage.getItem("memberInfo") || "{}");

    // Lấy từ user profile hoặc storedInfo
    const bloodGroup = user?.profile?.bloodGroup || storedInfo.bloodGroup || "";
    const rhType = user?.profile?.rhType || storedInfo.rhType || "";

    // Nếu có cả bloodGroup và rhType thì kết hợp lại
    if (bloodGroup && rhType) {
      return `${bloodGroup}-${rhType}`;
    }
    // Nếu chỉ có bloodGroup
    else if (bloodGroup) {
      return bloodGroup;
    }
    // Nếu không có thông tin
    else {
      return "Chưa xác định";
    }
  };

  // Custom hero section for members
  const MemberHeroSection = () => (
    <section
      className="hero-section member-hero"
      data-aos="fade-up"
      style={{
        backgroundImage: `url(${blood1})`,
        backgroundRepeat: "no-repeat",
        backgroundPosition: "center center",
        backgroundSize: "cover",
      }}
    >
      <div className="hero-container">
        <div className="hero-content">
          <h1 className="merriweather-title">
            CHÀO MỪNG, {userName.toUpperCase()}
            <br />
            CÙNG CHIA SẺ YÊU THƯƠNG
          </h1>
          <p className="merriweather-content">
            Cảm ơn bạn đã tham gia cộng đồng hiến máu. Hãy chọn hành động phù
            hợp để góp phần cứu sống những sinh mạng quý giá.
          </p>
          <div className="cta-row">
            <button
              onClick={handleBloodDonationClick}
              className="cta-button"
              disabled={isChecking}
            >
              {isChecking ? "ĐANG KIỂM TRA..." : "ĐĂNG KÝ HIẾN MÁU"}
            </button>
            <button
              onClick={handleBloodRequestClick}
              className="cta-button secondary"
              disabled={isChecking}
            >
              {isChecking ? "ĐANG KIỂM TRA..." : "ĐĂNG KÝ NHẬN MÁU"}
            </button>
          </div>
          <div className="member-info">
            <div className="info-item">
              <span className="label">Nhóm máu:</span>
              <span className="blood-type-badge">{getBloodTypeInfo()}</span>
            </div>
          </div>
        </div>
        <div className="hero-image">
          <img src={blood1} alt="Truyền máu" className="hero-img" />
        </div>
      </div>
    </section>
  );

  return (
    <>
      <GuestHomePage
        CustomNavbar={MemberNavbar}
        CustomHeroSection={MemberHeroSection}
      />

      {/* Unified Modal */}
      <UnifiedModal
        visible={showModal}
        onClose={() => setShowModal(false)}
        type={modalType}
        context={modalContext}
        profileCheckResult={profileCheckResult}
        pendingRequest={pendingRequest}
        onGoToProfile={() => {
          setShowModal(false);
          navigate("/member/profile");
        }}
        onViewHistory={() => {
          setShowModal(false);
          navigate("/member/activity-history");
        }}
      />
    </>
  );
};

export default MemberHomePage;
