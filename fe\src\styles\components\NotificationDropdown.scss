@use "../base/variables" as vars;

.notification-dropdown {
  position: relative;
  display: inline-block;

  .notification-trigger {
    position: relative;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.75rem;
    border-radius: 50%;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: rgba(0, 0, 0, 0.1);
      transform: scale(1.1);
    }

    .notification-icon {
      font-size: 1.5rem;
      color: #495057;
    }

    .notification-badge {
      position: absolute;
      top: 0.25rem;
      right: 0.25rem;
      background: linear-gradient(45deg, #dc3545, #c82333);
      color: white;
      font-size: 0.7rem;
      font-weight: 700;
      padding: 0.2rem 0.4rem;
      border-radius: 10px;
      min-width: 18px;
      height: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
      animation: pulse 2s infinite;
    }
  }

  .notification-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    width: 380px;
    max-height: 500px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    border: 1px solid #e9ecef;
    z-index: 1000;
    overflow: hidden;
    animation: slideDown 0.3s ease;

    .notification-header {
      padding: 1.5rem;
      background: linear-gradient(135deg, #f8f9fa, #e9ecef);
      border-bottom: 1px solid #e9ecef;
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
        font-size: 1.2rem;
        font-weight: 700;
        color: #495057;
      }

      .mark-all-read-btn {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-size: 0.8rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }
      }
    }

    .notification-list {
      max-height: 350px;
      overflow-y: auto;

      // Custom scrollbar
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f8f9fa;
      }

      &::-webkit-scrollbar-thumb {
        background: #dee2e6;
        border-radius: 3px;

        &:hover {
          background: #adb5bd;
        }
      }

      .notification-loading {
        padding: 3rem;
        text-align: center;
        color: #6c757d;

        .loading-spinner {
          width: 30px;
          height: 30px;
          border: 3px solid #f8f9fa;
          border-top: 3px solid #28a745;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 1rem;
        }
      }

      .notification-empty {
        padding: 3rem;
        text-align: center;
        color: #6c757d;

        .empty-icon {
          font-size: 3rem;
          display: block;
          margin-bottom: 1rem;
        }

        p {
          margin: 0;
          font-size: 1rem;
        }
      }

      .notification-item {
        border-bottom: 1px solid #f8f9fa;
        transition: all 0.3s ease;

        &:hover {
          background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        }

        &.unread {
          background: linear-gradient(
            135deg,
            rgba(40, 167, 69, 0.05),
            rgba(32, 201, 151, 0.05)
          );
          border-left: 4px solid #28a745;
        }

        .notification-content {
          padding: 1rem;
          display: flex;
          gap: 1rem;
          align-items: flex-start;

          .notification-icon-wrapper {
            position: relative;
            flex-shrink: 0;

            .notification-type-icon {
              font-size: 1.5rem;
              display: block;
            }

            .unread-dot {
              position: absolute;
              top: -2px;
              right: -2px;
              width: 8px;
              height: 8px;
              background: #dc3545;
              border-radius: 50%;
              border: 2px solid white;
            }
          }

          .notification-body {
            flex: 1;
            min-width: 0;

            .notification-title {
              font-weight: 700;
              color: #495057;
              margin-bottom: 0.5rem;
              font-size: 0.95rem;
            }

            .notification-message {
              color: #6c757d;
              font-size: 0.9rem;
              line-height: 1.4;
              margin-bottom: 0.5rem;
              word-wrap: break-word;
            }

            .notification-time {
              color: #adb5bd;
              font-size: 0.8rem;
              margin-bottom: 0.75rem;
            }

            .notification-action {
              display: inline-block;
              background: linear-gradient(45deg, #17a2b8, #20c997);
              color: white;
              text-decoration: none;
              padding: 0.4rem 0.8rem;
              border-radius: 6px;
              font-size: 0.8rem;
              font-weight: 600;
              transition: all 0.3s ease;

              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
                text-decoration: none;
                color: white;
              }
            }
          }

          .notification-actions {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            flex-shrink: 0;

            .mark-read-btn,
            .delete-btn {
              width: 24px;
              height: 24px;
              border: none;
              border-radius: 50%;
              cursor: pointer;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 0.8rem;
              font-weight: 700;
              transition: all 0.3s ease;
            }

            .mark-read-btn {
              background: linear-gradient(45deg, #28a745, #20c997);
              color: white;

              &:hover {
                transform: scale(1.1);
                box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
              }
            }

            .delete-btn {
              background: linear-gradient(45deg, #dc3545, #c82333);
              color: white;

              &:hover {
                transform: scale(1.1);
                box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
              }
            }
          }
        }
      }
    }

    .notification-footer {
      padding: 1rem;
      background: linear-gradient(135deg, #f8f9fa, #e9ecef);
      border-top: 1px solid #e9ecef;
      text-align: center;

      .view-all-link {
        color: #28a745;
        text-decoration: none;
        font-weight: 600;
        font-size: 0.9rem;
        transition: all 0.3s ease;

        &:hover {
          color: #20c997;
          text-decoration: underline;
        }
      }
    }
  }
}

// Animations
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Responsive
@media (max-width: 768px) {
  .notification-dropdown {
    .notification-dropdown-menu {
      width: 320px;
      right: -50px;

      .notification-list {
        max-height: 300px;

        .notification-item .notification-content {
          padding: 0.75rem;
          gap: 0.75rem;

          .notification-body {
            .notification-title {
              font-size: 0.9rem;
            }

            .notification-message {
              font-size: 0.85rem;
            }
          }
        }
      }
    }
  }
}
