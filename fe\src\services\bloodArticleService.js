// Service for BloodArticles API
import { apiClient } from "./axiosInstance";
import config from "../config/environment";

const API_URL = config.api.bloodArticles;

export async function getBloodArticles() {
  const response = await apiClient.get(API_URL);
  return response.data;
}

export async function createArticle(data) {
  // Process tags to separate existing tag IDs and new tag names
  const selectedTags = data.tagIds || [];
  const tagIds = [];
  const newTags = [];

  selectedTags.forEach((tag) => {
    // If it's a number or numeric string, treat as tagId
    if (
      typeof tag === "number" ||
      (typeof tag === "string" && !isNaN(tag) && tag.trim() !== "")
    ) {
      tagIds.push(typeof tag === "number" ? tag : parseInt(tag));
    } else if (typeof tag === "string" && tag.trim() !== "") {
      // If it's a non-numeric string, treat as new tag name
      newTags.push(tag);
    }
  });

  const requestData = {
    title: data.title || "",
    content: data.content || "",
    imgUrl: data.imgUrl || "",
    tagIds: tagIds,
    newTags: newTags,
    userId: data.userId,
  };

  console.log("BloodArticle Service - Request data:", requestData);

  try {
    const response = await apiClient.post(API_URL, requestData);
    console.log("BloodArticle Service - Response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Create article error:", error);
    throw error;
  }
}

export async function updateArticle(articleId, data) {
  // Process tags to separate existing tag IDs and new tag names
  const selectedTags = data.tagIds || [];
  const tagIds = [];
  const newTags = [];

  selectedTags.forEach((tag) => {
    // If it's a number or numeric string, treat as tagId
    if (
      typeof tag === "number" ||
      (typeof tag === "string" && !isNaN(tag) && tag.trim() !== "")
    ) {
      tagIds.push(typeof tag === "number" ? tag : parseInt(tag));
    } else if (typeof tag === "string" && tag.trim() !== "") {
      // If it's a non-numeric string, treat as new tag name
      newTags.push(tag);
    }
  });

  const requestData = {
    title: data.title || "",
    content: data.content || "",
    imgUrl: data.imgUrl || "",
    tagIds: tagIds,
    newTags: newTags,
    userId: data.userId,
  };

  try {
    const response = await apiClient.put(
      `${API_URL}/${articleId}`,
      requestData
    );
    return response.data;
  } catch (error) {
    console.error("Update article error:", error);
    throw error;
  }
}

export async function updateBlog(articleId, data) {
  // DEPRECATED: This function has confusing logic that creates articles when ID is missing
  // Use updateArticle() directly for updates and createArticle() for creation
  console.warn(
    "updateBlog is deprecated. Use updateArticle() or createArticle() directly."
  );

  if (
    !articleId ||
    articleId === "" ||
    articleId === null ||
    articleId === undefined
  ) {
    throw new Error(
      "Article ID is required for update. Use createArticle() for new articles."
    );
  }

  return updateArticle(articleId, data);
}

export async function deleteArticle(articleId) {
  // Get current user ID from localStorage
  const currentUser = localStorage.getItem("currentUser");
  let userId = null;
  if (currentUser) {
    try {
      const userData = JSON.parse(currentUser);
      userId = userData.id || userData.userId || userData.userID;
    } catch (error) {
      console.error("Error parsing currentUser:", error);
    }
  }

  console.log("Deleting article with ID:", articleId);
  console.log("User ID:", userId);

  try {
    // Send userId as query parameter instead of request body
    const url = userId
      ? `${API_URL}/${articleId}?userId=${userId}`
      : `${API_URL}/${articleId}`;

    const response = await apiClient.delete(url, {
      timeout: 10000, // 10 second timeout
    });
    return response.data;
  } catch (error) {
    console.error("Error deleting article:", error);
    throw error;
  }
}

export async function getBloodArticleDetail(articleId) {
  try {
    console.log("Trying to get article detail for ID:", articleId);

    // Use direct API call to the specific endpoint
    const apiUrl = `https://localhost:7021/api/BloodArticles/${articleId}`;
    console.log("Fetching article from URL:", apiUrl);

    const response = await apiClient.get(apiUrl, {
      // Handle HTTPS localhost issues
      httpsAgent: false,
    });

    console.log("API Response:", response.data);
    console.log("Response status:", response.status);

    const responseData = response.data;

    // Check if response is successful and has data
    if (response.status === 200 && responseData) {
      // If API returns object directly
      if (responseData.articleId || responseData.id) {
        console.log("Found article object:", responseData);
        return responseData;
      }

      // If API returns array, find the article
      if (Array.isArray(responseData)) {
        console.log("Response is array with length:", responseData.length);

        const article = responseData.find((item) => {
          return (
            item.articleId === parseInt(articleId) ||
            item.articleId === articleId ||
            item.id === parseInt(articleId) ||
            item.id === articleId
          );
        });

        if (article) {
          console.log("Found article in array:", article);
          return article;
        } else {
          console.log("Article not found in array");
          throw new Error(`Article with ID ${articleId} not found`);
        }
      }

      // If response has unexpected format
      console.log("Unexpected response format:", responseData);
      return responseData;
    } else {
      throw new Error(`API returned status ${response.status}`);
    }
  } catch (error) {
    console.error("Error fetching article detail:", error);

    // Try fallback with HTTP if HTTPS fails
    if (error.code === "ECONNREFUSED" || error.message.includes("localhost")) {
      try {
        console.log("Trying HTTP fallback...");
        const httpUrl = `http://localhost:7021/api/BloodArticles/${articleId}`;

        const response = await apiClient.get(httpUrl);

        console.log("HTTP Fallback Response:", response.data);
        return response.data;
      } catch (httpError) {
        console.error("HTTP fallback also failed:", httpError);
      }
    }

    throw error;
  }
}
